# 坦克大战游戏

这是一个基于HTML5 Canvas的坦克大战小游戏，类似于经典的坦克大战游戏。

## 游戏功能

- 玩家控制蓝色坦克，通过方向键移动，空格键射击
- 敌人是红色坦克，会自动移动和随机射击
- 三种类型的障碍物：
  - 砖墙（棕色）：玩家子弹可以摧毁
  - 钢墙（灰色）：不可摧毁
  - 水域（蓝色透明）：可以穿过但有视觉效果
- 计分系统：击中敌人获得分数
- 生命值系统：被敌人击中会减少生命值
- 关卡系统：消灭所有敌人进入下一关，敌人数量随关卡增加

## 如何运行

1. 双击打开`index.html`文件即可在浏览器中运行游戏
2. 点击"开始游戏"按钮开始
3. 使用方向键控制坦克移动，空格键发射子弹
4. 点击"暂停"按钮可以暂停游戏，点击"重新开始"按钮可以重置游戏

## 游戏规则

- 玩家有3条生命
- 每消灭一个敌人获得100分
- 每通过一关，敌人数量会增加
- 游戏结束条件：玩家生命值为0

## 技术说明

- 使用HTML5 Canvas绘制游戏画面
- 使用JavaScript实现游戏逻辑
- 使用CSS样式美化游戏界面

## 游戏截图

游戏运行后，你将看到一个600x600像素的游戏画面，包含玩家坦克、敌人坦克、障碍物和游戏信息面板。
运行  python -m http.server 8888  打开浏览器访问 http://localhost:8888 即可运行游戏
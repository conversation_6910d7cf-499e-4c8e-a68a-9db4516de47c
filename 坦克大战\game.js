// 游戏基本设置
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');
const livesElement = document.getElementById('lives');
const levelElement = document.getElementById('level');
const startBtn = document.getElementById('startBtn');
const pauseBtn = document.getElementById('pauseBtn');
const restartBtn = document.getElementById('restartBtn');

// 游戏状态
let gameState = {
    isRunning: false,
    isPaused: false,
    score: 0,
    lives: 3,
    level: 1,
    keys: {
        ArrowUp: false,
        ArrowDown: false,
        ArrowLeft: false,
        ArrowRight: false,
        space: false
    }
};

// 游戏常量
const CELL_SIZE = 30;
const CANVAS_WIDTH = canvas.width;
const CANVAS_HEIGHT = canvas.height;
const PLAYER_SPEED = 2;
const ENEMY_SPEED = 1;
const BULLET_SPEED = 4;
const PLAYER_FIRE_RATE = 500; // 毫秒
const ENEMY_FIRE_RATE = 2000; // 毫秒

// 游戏对象
let player = null;
let enemies = [];
let bullets = [];
let obstacles = [];
let lastPlayerShot = 0;

// 玩家坦克类
class Tank {
    constructor(x, y, width, height, color, isPlayer = false) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.color = color;
        this.direction = 'up';
        this.speed = isPlayer ? PLAYER_SPEED : ENEMY_SPEED;
        this.isPlayer = isPlayer;
        this.lastShot = 0;
    }

    draw() {
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // 绘制炮管
        ctx.fillStyle = '#333';
        switch(this.direction) {
            case 'up':
                ctx.fillRect(this.x + this.width/2 - 2, this.y - 10, 4, 10);
                break;
            case 'down':
                ctx.fillRect(this.x + this.width/2 - 2, this.y + this.height, 4, 10);
                break;
            case 'left':
                ctx.fillRect(this.x - 10, this.y + this.height/2 - 2, 10, 4);
                break;
            case 'right':
                ctx.fillRect(this.x + this.width, this.y + this.height/2 - 2, 10, 4);
                break;
        }
    }

    move() {
        let newX = this.x;
        let newY = this.y;

        if (this.isPlayer) {
            if (gameState.keys.ArrowUp) {
                newY -= this.speed;
                this.direction = 'up';
            }
            if (gameState.keys.ArrowDown) {
                newY += this.speed;
                this.direction = 'down';
            }
            if (gameState.keys.ArrowLeft) {
                newX -= this.speed;
                this.direction = 'left';
            }
            if (gameState.keys.ArrowRight) {
                newX += this.speed;
                this.direction = 'right';
            }
        } else {
            // 敌人AI简单移动
            const directions = ['up', 'down', 'left', 'right'];
            if (Math.random() < 0.01) { // 1% chance to change direction
                this.direction = directions[Math.floor(Math.random() * directions.length)];
            }

            switch(this.direction) {
                case 'up':
                    newY -= this.speed;
                    break;
                case 'down':
                    newY += this.speed;
                    break;
                case 'left':
                    newX -= this.speed;
                    break;
                case 'right':
                    newX += this.speed;
                    break;
            }
        }

        // 边界检查
        if (newX >= 0 && newX <= CANVAS_WIDTH - this.width && 
            newY >= 0 && newY <= CANVAS_HEIGHT - this.height) {
            // 障碍物碰撞检查
            let canMove = true;
            for (const obstacle of obstacles) {
                if (this.checkCollision(newX, newY, obstacle)) {
                    canMove = false;
                    break;
                }
            }

            if (canMove) {
                this.x = newX;
                this.y = newY;
            }
        }
    }

    shoot() {
        const now = Date.now();
        const fireRate = this.isPlayer ? PLAYER_FIRE_RATE : ENEMY_FIRE_RATE;
        
        if (now - this.lastShot < fireRate) return;
        
        this.lastShot = now;
        
        let bulletX, bulletY;
        
        switch(this.direction) {
            case 'up':
                bulletX = this.x + this.width / 2 - 2;
                bulletY = this.y - 10;
                break;
            case 'down':
                bulletX = this.x + this.width / 2 - 2;
                bulletY = this.y + this.height;
                break;
            case 'left':
                bulletX = this.x - 10;
                bulletY = this.y + this.height / 2 - 2;
                break;
            case 'right':
                bulletX = this.x + this.width;
                bulletY = this.y + this.height / 2 - 2;
                break;
        }
        
        bullets.push(new Bullet(bulletX, bulletY, 4, 10, this.direction, this.isPlayer));
    }

    checkCollision(x, y, obj) {
        return x < obj.x + obj.width &&
               x + this.width > obj.x &&
               y < obj.y + obj.height &&
               y + this.height > obj.y;
    }
}

// 子弹类
class Bullet {
    constructor(x, y, width, height, direction, isPlayerBullet) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.direction = direction;
        this.isPlayerBullet = isPlayerBullet;
        this.speed = BULLET_SPEED;
    }

    draw() {
        ctx.fillStyle = this.isPlayerBullet ? '#0f0' : '#f00';
        ctx.fillRect(this.x, this.y, this.width, this.height);
    }

    move() {
        switch(this.direction) {
            case 'up':
                this.y -= this.speed;
                break;
            case 'down':
                this.y += this.speed;
                break;
            case 'left':
                this.x -= this.speed;
                break;
            case 'right':
                this.x += this.speed;
                break;
        }
    }

    isOffScreen() {
        return this.x < -this.width ||
               this.x > CANVAS_WIDTH ||
               this.y < -this.height ||
               this.y > CANVAS_HEIGHT;
    }

    checkCollision(obj) {
        return this.x < obj.x + obj.width &&
               this.x + this.width > obj.x &&
               this.y < obj.y + obj.height &&
               this.y + this.height > obj.y;
    }
}

// 障碍物类
class Obstacle {
    constructor(x, y, width, height, type = 'brick') {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.type = type; // brick, steel, water
        this.destroyable = type === 'brick';
    }

    draw() {
        switch(this.type) {
            case 'brick':
                ctx.fillStyle = '#A0522D';
                break;
            case 'steel':
                ctx.fillStyle = '#C0C0C0';
                break;
            case 'water':
                ctx.fillStyle = '#0000FF';
                ctx.globalAlpha = 0.5;
                break;
        }
        
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        if (this.type === 'water') {
            ctx.globalAlpha = 1;
        }
    }
}

// 初始化游戏
function initGame() {
    // 重置游戏状态
    gameState.score = 0;
    gameState.lives = 3;
    gameState.level = 1;
    gameState.isRunning = true;
    gameState.isPaused = false;
    
    // 更新UI
    scoreElement.textContent = gameState.score;
    livesElement.textContent = gameState.lives;
    levelElement.textContent = gameState.level;
    
    // 清空游戏对象
    bullets = [];
    enemies = [];
    obstacles = [];
    
    // 创建玩家
    player = new Tank(
        CANVAS_WIDTH / 2 - CELL_SIZE / 2,
        CANVAS_HEIGHT - CELL_SIZE * 2,
        CELL_SIZE, CELL_SIZE, '#00f', true
    );
    
    // 创建障碍物
    createObstacles();
    
    // 创建敌人
    createEnemies();
}

// 创建障碍物
function createObstacles() {
    // 边缘墙
    for (let i = 0; i < CANVAS_WIDTH; i += CELL_SIZE) {
        obstacles.push(new Obstacle(i, 0, CELL_SIZE, CELL_SIZE, 'steel'));
        obstacles.push(new Obstacle(i, CANVAS_HEIGHT - CELL_SIZE, CELL_SIZE, CELL_SIZE, 'steel'));
    }
    
    for (let i = CELL_SIZE; i < CANVAS_HEIGHT - CELL_SIZE; i += CELL_SIZE) {
        obstacles.push(new Obstacle(0, i, CELL_SIZE, CELL_SIZE, 'steel'));
        obstacles.push(new Obstacle(CANVAS_WIDTH - CELL_SIZE, i, CELL_SIZE, CELL_SIZE, 'steel'));
    }
    
    // 随机砖墙
    for (let i = 0; i < 30; i++) {
        const x = Math.floor(Math.random() * (CANVAS_WIDTH / CELL_SIZE - 4)) * CELL_SIZE + CELL_SIZE * 2;
        const y = Math.floor(Math.random() * (CANVAS_HEIGHT / CELL_SIZE - 8)) * CELL_SIZE + CELL_SIZE * 2;
        
        // 确保不阻挡玩家出生位置
        if (!(x >= CANVAS_WIDTH/2 - CELL_SIZE*2 && x <= CANVAS_WIDTH/2 + CELL_SIZE*2 &&
              y >= CANVAS_HEIGHT - CELL_SIZE*4 && y <= CANVAS_HEIGHT - CELL_SIZE*2)) {
            obstacles.push(new Obstacle(x, y, CELL_SIZE, CELL_SIZE, 'brick'));
        }
    }
    
    // 随机水域
    for (let i = 0; i < 5; i++) {
        const x = Math.floor(Math.random() * (CANVAS_WIDTH / CELL_SIZE - 4)) * CELL_SIZE + CELL_SIZE * 2;
        const y = Math.floor(Math.random() * (CANVAS_HEIGHT / CELL_SIZE - 8)) * CELL_SIZE + CELL_SIZE * 2;
        
        obstacles.push(new Obstacle(x, y, CELL_SIZE, CELL_SIZE, 'water'));
    }
}

// 创建敌人
function createEnemies() {
    const enemyCount = 3 + gameState.level - 1;
    for (let i = 0; i < enemyCount; i++) {
        const x = CELL_SIZE * 2 + (i % 3) * CELL_SIZE * 4;
        const y = CELL_SIZE * 2 + Math.floor(i / 3) * CELL_SIZE * 3;
        enemies.push(new Tank(x, y, CELL_SIZE, CELL_SIZE, '#f00'));
    }
}

// 游戏主循环
function gameLoop() {
    if (!gameState.isRunning) return;
    if (gameState.isPaused) {
        requestAnimationFrame(gameLoop);
        return;
    }

    // 清空画布
    ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    // 绘制和更新所有游戏对象
    drawObstacles();
    updatePlayer();
    updateEnemies();
    updateBullets();
    checkCollisions();

    // 检查游戏胜利条件
    if (enemies.length === 0) {
        gameState.level++;
        levelElement.textContent = gameState.level;
        createEnemies();
    }

    requestAnimationFrame(gameLoop);
}

// 绘制障碍物
function drawObstacles() {
    for (const obstacle of obstacles) {
        obstacle.draw();
    }
}

// 更新玩家
function updatePlayer() {
    player.move();
    player.draw();
    
    // 玩家射击
    if (gameState.keys.space) {
        player.shoot();
    }
}

// 更新敌人
function updateEnemies() {
    for (const enemy of enemies) {
        enemy.move();
        enemy.draw();
        
        // 敌人随机射击
        if (Math.random() < 0.01) {
            enemy.shoot();
        }
    }
}

// 更新子弹
function updateBullets() {
    for (let i = bullets.length - 1; i >= 0; i--) {
        const bullet = bullets[i];
        bullet.move();
        
        // 检查子弹是否超出屏幕
        if (bullet.isOffScreen()) {
            bullets.splice(i, 1);
            continue;
        }
        
        // 检查子弹是否击中障碍物
        let hitObstacle = false;
        for (let j = obstacles.length - 1; j >= 0; j--) {
            const obstacle = obstacles[j];
            if (bullet.checkCollision(obstacle) && obstacle.type !== 'water') {
                if (obstacle.destroyable && bullet.isPlayerBullet) {
                    obstacles.splice(j, 1);
                }
                bullets.splice(i, 1);
                hitObstacle = true;
                break;
            }
        }
        
        if (hitObstacle) continue;
        
        bullet.draw();
    }
}

// 碰撞检测
function checkCollisions() {
    // 检查子弹是否击中敌人
    for (let i = bullets.length - 1; i >= 0; i--) {
        const bullet = bullets[i];
        
        if (bullet.isPlayerBullet) {
            // 玩家子弹击中敌人
            for (let j = enemies.length - 1; j >= 0; j--) {
                const enemy = enemies[j];
                if (bullet.checkCollision(enemy)) {
                    enemies.splice(j, 1);
                    bullets.splice(i, 1);
                    gameState.score += 100;
                    scoreElement.textContent = gameState.score;
                    break;
                }
            }
        } else {
            // 敌人子弹击中玩家
            if (bullet.checkCollision(player)) {
                bullets.splice(i, 1);
                gameState.lives--;
                livesElement.textContent = gameState.lives;
                
                if (gameState.lives <= 0) {
                    gameOver();
                } else {
                    // 重置玩家位置
                    player.x = CANVAS_WIDTH / 2 - CELL_SIZE / 2;
                    player.y = CANVAS_HEIGHT - CELL_SIZE * 2;
                }
            }
        }
    }
}

// 游戏结束
function gameOver() {
    gameState.isRunning = false;
    alert(`游戏结束！得分：${gameState.score}，关卡：${gameState.level}`);
}

// 事件监听
startBtn.addEventListener('click', () => {
    try {
        if (!gameState.isRunning) {
            // 初始化游戏对象
            initGame();
            gameState.isRunning = true;
            gameState.isPaused = false;
            gameLoop();
        } else if (gameState.isPaused) {
            gameState.isPaused = false;
        }
    } catch (error) {
        console.error('游戏启动错误:', error);
        alert('游戏启动失败，请刷新页面重试');
    }
});

pauseBtn.addEventListener('click', () => {
    if (gameState.isRunning) {
        gameState.isPaused = !gameState.isPaused;
    }
});

restartBtn.addEventListener('click', () => {
    initGame();
    if (!gameState.isRunning) {
        gameLoop();
    }
});

// 键盘控制
window.addEventListener('keydown', (e) => {
    if (e.key === 'ArrowUp' || e.key === 'ArrowDown' || 
        e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
        gameState.keys[e.key] = true;
    } else if (e.key === ' ') {
        gameState.keys.space = true;
    }
});

window.addEventListener('keyup', (e) => {
    if (e.key === 'ArrowUp' || e.key === 'ArrowDown' || 
        e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
        gameState.keys[e.key] = false;
    } else if (e.key === ' ') {
        gameState.keys.space = false;
    }
});

// 等待DOM加载完成后初始化游戏
window.addEventListener('DOMContentLoaded', () => {
    // 初始化游戏状态
    gameState.isRunning = false;
    gameState.isPaused = false;
    gameState.score = 0;
    gameState.lives = 3;
    gameState.level = 1;
    
    // 更新UI
    scoreElement.textContent = gameState.score;
    livesElement.textContent = gameState.lives;
    levelElement.textContent = gameState.level;
    
    // 清空游戏对象
    player = null;
    bullets = [];
    enemies = [];
    obstacles = [];
    
    // 在画布上显示初始提示信息
    showStartMessage();
})

// 在画布上显示开始游戏提示
function showStartMessage() {
    ctx.fillStyle = '#fff';
    ctx.font = '30px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('点击开始游戏按钮开始', CANVAS_WIDTH/2, CANVAS_HEIGHT/2);
    ctx.font = '20px Arial';
    ctx.fillText('使用方向键移动，空格键射击', CANVAS_WIDTH/2, CANVAS_HEIGHT/2 + 40);
}
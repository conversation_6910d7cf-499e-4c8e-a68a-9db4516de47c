// 餐厅详情页面的脚本

document.addEventListener('DOMContentLoaded', function() {
    // 获取元素
    const menuTabs = document.querySelectorAll('.menu-tabs .tab');
    const foodOptions = document.querySelectorAll('.food-options button');
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    const cartBtn = document.getElementById('cartBtn');
    const cartBottom = document.querySelector('.cart-bottom');
    const cartSidebar = document.querySelector('.cart-sidebar');
    const closeCart = document.getElementById('closeCart');
    const checkoutBtn = document.getElementById('checkoutBtn');
    const checkoutBtnSidebar = document.getElementById('checkoutBtnSidebar');
    const checkoutModal = document.querySelector('.checkout-modal');
    const confirmOrderBtn = document.querySelector('.confirm-order-btn');
    const successModal = document.querySelector('.success-modal');
    const closeSuccess = document.querySelector('.close-success');
    const cartCount = document.querySelectorAll('.cart-count');
    const itemCount = document.getElementById('itemCount');
    const itemCountSidebar = document.getElementById('itemCountSidebar');
    const totalPrice = document.getElementById('totalPrice');
    const totalPriceSidebar = document.getElementById('totalPriceSidebar');
    const orderSubtotal = document.getElementById('orderSubtotal');
    const orderTotal = document.getElementById('orderTotal');
    const cartItemsContainer = document.querySelector('.cart-items');
    const orderItemsContainer = document.querySelector('.order-items');
    const overlay = document.querySelector('.overlay');
    const favoriteBtn = document.querySelector('.favorite-btn');
    const actionBtns = document.querySelectorAll('.action-btn');
    
    // 购物车数据
    let cart = [];
    let isFavorite = false;
    
    // 初始化
    init();
    
    function init() {
        // 事件监听
        setupEventListeners();
        
        // 更新购物车UI
        updateCartUI();
    }
    
    function setupEventListeners() {
        // 菜单标签切换
        menuTabs.forEach(tab => {
            tab.addEventListener('click', switchMenuTab);
        });
        
        // 食物选项（辣度、份量等）
        foodOptions.forEach(option => {
            option.addEventListener('click', selectFoodOption);
        });
        
        // 添加到购物车
        addToCartButtons.forEach(button => {
            button.addEventListener('click', addToCart);
        });
        
        // 购物车交互
        cartBtn.addEventListener('click', openCart);
        cartBottom.addEventListener('click', openCart);
        closeCart.addEventListener('click', closeCartSidebar);
        checkoutBtn.addEventListener('click', openCheckout);
        checkoutBtnSidebar.addEventListener('click', openCheckout);
        
        // 结算流程
        confirmOrderBtn.addEventListener('click', processOrder);
        closeSuccess.addEventListener('click', closeSuccessModal);
        
        // 收藏按钮
        favoriteBtn.addEventListener('click', toggleFavorite);
        
        // 商家、优惠等按钮
        actionBtns.forEach(btn => {
            btn.addEventListener('click', handleActionBtn);
        });
        
        // 关闭按钮
        document.querySelectorAll('.modal-content .close-btn').forEach(btn => {
            btn.addEventListener('click', closeCheckout);
        });
        
        // 遮罩层点击
        overlay.addEventListener('click', closeAllModals);
    }
    
    // 菜单标签切换
    function switchMenuTab() {
        // 移除所有标签的活动状态
        menuTabs.forEach(tab => tab.classList.remove('active'));
        
        // 添加当前标签的活动状态
        this.classList.add('active');
        
        // 在实际应用中，这里可以根据标签显示不同的菜单内容
        const tabIndex = Array.from(menuTabs).indexOf(this);
        console.log(`切换到菜单标签：${tabIndex}`);
        
        // 模拟加载不同分类的菜单
        // 这里可以添加实际的加载逻辑
    }
    
    // 选择食物选项
    function selectFoodOption() {
        // 获取同级按钮并移除活动状态
        const siblings = Array.from(this.parentElement.children).filter(child => 
            child.tagName === 'BUTTON'
        );
        
        siblings.forEach(sibling => sibling.classList.remove('active'));
        
        // 添加当前按钮的活动状态
        this.classList.add('active');
        
        console.log(`选择了选项：${this.textContent}`);
    }
    
    // 添加商品到购物车
    function addToCart(event) {
        const button = event.target.closest('.add-to-cart');
        const id = button.dataset.id;
        const name = button.dataset.name;
        const price = parseFloat(button.dataset.price);
        const image = button.dataset.image;
        
        // 检查商品是否已在购物车中
        const existingItem = cart.find(item => item.id === id);
        
        if (existingItem) {
            existingItem.quantity++;
        } else {
            cart.push({
                id,
                name,
                price,
                image,
                quantity: 1
            });
        }
        
        // 显示添加成功动画
        showAddToCartAnimation(button);
        
        // 更新购物车UI
        updateCartUI();
    }
    
    // 显示添加到购物车动画
    function showAddToCartAnimation(button) {
        const animation = document.createElement('span');
        animation.className = 'add-animation';
        animation.innerHTML = '\u2713';
        animation.style.position = 'fixed';
        animation.style.width = '30px';
        animation.style.height = '30px';
        animation.style.borderRadius = '50%';
        animation.style.backgroundColor = '#ff4d4f';
        animation.style.color = 'white';
        animation.style.display = 'flex';
        animation.style.alignItems = 'center';
        animation.style.justifyContent = 'center';
        animation.style.zIndex = '1500';
        animation.style.opacity = '0';
        
        // 获取按钮位置
        const rect = button.getBoundingClientRect();
        animation.style.left = `${rect.left + rect.width / 2}px`;
        animation.style.top = `${rect.top + rect.height / 2}px`;
        
        document.body.appendChild(animation);
        
        // 动画效果
        setTimeout(() => {
            animation.style.transition = 'all 0.8s cubic-bezier(0.2, 1, 0.3, 1)';
            animation.style.opacity = '1';
            animation.style.transform = 'scale(1.2)';
        }, 10);
        
        // 获取购物车图标位置
        const cartRect = cartBtn.getBoundingClientRect();
        
        setTimeout(() => {
            animation.style.transition = 'all 1s cubic-bezier(0.2, 1, 0.3, 1)';
            animation.style.left = `${cartRect.left + cartRect.width / 2}px`;
            animation.style.top = `${cartRect.top + cartRect.height / 2}px`;
            animation.style.opacity = '0';
            animation.style.transform = 'scale(0.5)';
        }, 300);
        
        // 移除动画元素
        setTimeout(() => {
            document.body.removeChild(animation);
        }, 1300);
    }
    
    // 更新购物车UI
    function updateCartUI() {
        // 更新购物车数量
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.forEach(count => count.textContent = totalItems);
        itemCount.textContent = totalItems;
        itemCountSidebar.textContent = totalItems;
        
        // 清空购物车容器
        cartItemsContainer.innerHTML = '';
        
        if (cart.length === 0) {
            // 显示空购物车状态
            const emptyCart = document.createElement('div');
            emptyCart.className = 'empty-cart';
            emptyCart.innerHTML = `
                <i class="fas fa-shopping-cart"></i>
                <p>购物车还是空的</p>
                <p>快去添加美食吧！</p>
            `;
            cartItemsContainer.appendChild(emptyCart);
            
            // 禁用结算按钮
            checkoutBtn.disabled = true;
            checkoutBtn.style.opacity = '0.5';
            checkoutBtnSidebar.disabled = true;
            checkoutBtnSidebar.style.opacity = '0.5';
            
            // 更新总价
            totalPrice.textContent = '¥0';
            totalPriceSidebar.textContent = '¥0';
        } else {
            // 启用结算按钮
            checkoutBtn.disabled = false;
            checkoutBtn.style.opacity = '1';
            checkoutBtnSidebar.disabled = false;
            checkoutBtnSidebar.style.opacity = '1';
            
            // 添加购物车项目
            cart.forEach(item => {
                const cartItem = document.createElement('div');
                cartItem.className = 'cart-item';
                cartItem.innerHTML = `
                    <div class="cart-item-img">
                        <img src="${item.image}" alt="${item.name}">
                    </div>
                    <div class="cart-item-info">
                        <h4 class="cart-item-name">${item.name}</h4>
                        <div class="cart-item-price">¥${item.price.toFixed(2)}</div>
                        <div class="cart-item-controls">
                            <button class="control-btn decrease" data-id="${item.id}">-</button>
                            <span class="quantity">${item.quantity}</span>
                            <button class="control-btn increase" data-id="${item.id}">+</button>
                        </div>
                    </div>
                `;
                cartItemsContainer.appendChild(cartItem);
            });
            
            // 添加增减按钮事件监听
            document.querySelectorAll('.decrease').forEach(btn => {
                btn.addEventListener('click', decreaseQuantity);
            });
            document.querySelectorAll('.increase').forEach(btn => {
                btn.addEventListener('click', increaseQuantity);
            });
            
            // 更新总价
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            totalPrice.textContent = `¥${total.toFixed(2)}`;
            totalPriceSidebar.textContent = `¥${total.toFixed(2)}`;
        }
    }
    
    // 减少商品数量
    function decreaseQuantity(event) {
        const id = event.target.closest('.decrease').dataset.id;
        const item = cart.find(item => item.id === id);
        
        if (item.quantity > 1) {
            item.quantity--;
        } else {
            cart = cart.filter(item => item.id !== id);
        }
        
        updateCartUI();
    }
    
    // 增加商品数量
    function increaseQuantity(event) {
        const id = event.target.closest('.increase').dataset.id;
        const item = cart.find(item => item.id === id);
        
        item.quantity++;
        
        updateCartUI();
    }
    
    // 打开购物车
    function openCart() {
        cartSidebar.classList.add('active');
        overlay.classList.add('active');
        document.body.classList.add('overflow-hidden');
        updateCartUI();
    }
    
    // 关闭购物车
    function closeCartSidebar() {
        cartSidebar.classList.remove('active');
        overlay.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
    }
    
    // 打开结算弹窗
    function openCheckout() {
        if (cart.length === 0) {
            alert('购物车为空，请先添加商品！');
            return;
        }
        
        cartSidebar.classList.remove('active');
        checkoutModal.classList.add('active');
        updateOrderSummary();
    }
    
    // 关闭结算弹窗
    function closeCheckout() {
        checkoutModal.classList.remove('active');
        overlay.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
    }
    
    // 处理订单
    function processOrder() {
        const name = document.querySelector('input[type="text"][placeholder="请输入收货人姓名"]').value;
        const phone = document.querySelector('input[type="tel"][placeholder="请输入联系电话"]').value;
        const address = document.querySelector('input[type="text"][placeholder="请输入详细地址"]').value;
        
        if (!name || !phone || !address) {
            alert('请填写完整的配送信息！');
            return;
        }
        
        checkoutModal.classList.remove('active');
        successModal.classList.add('active');
        
        // 清空购物车
        cart = [];
        updateCartUI();
    }
    
    // 关闭订单成功弹窗
    function closeSuccessModal() {
        successModal.classList.remove('active');
        overlay.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
    }
    
    // 关闭所有模态框
    function closeAllModals() {
        cartSidebar.classList.remove('active');
        checkoutModal.classList.remove('active');
        successModal.classList.remove('active');
        overlay.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
    }
    
    // 更新订单摘要
    function updateOrderSummary() {
        // 清空订单容器
        orderItemsContainer.innerHTML = '';
        
        // 添加订单项
        cart.forEach(item => {
            const orderItem = document.createElement('div');
            orderItem.className = 'cart-item';
            orderItem.innerHTML = `
                <div class="cart-item-img">
                    <img src="${item.image}" alt="${item.name}">
                </div>
                <div class="cart-item-info">
                    <h4 class="cart-item-name">${item.name}</h4>
                    <div class="cart-item-price">¥${item.price.toFixed(2)} x ${item.quantity}</div>
                </div>
            `;
            orderItemsContainer.appendChild(orderItem);
        });
        
        // 计算总价
        const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const deliveryFee = 5; // 配送费
        const total = subtotal + deliveryFee;
        
        // 更新价格显示
        orderSubtotal.textContent = `¥${subtotal.toFixed(2)}`;
        orderTotal.textContent = `¥${total.toFixed(2)}`;
    }
    
    // 切换收藏状态
    function toggleFavorite() {
        isFavorite = !isFavorite;
        const icon = favoriteBtn.querySelector('i');
        
        if (isFavorite) {
            icon.classList.remove('far');
            icon.classList.add('fas');
            icon.classList.add('text-red-500');
            favoriteBtn.querySelector('span').textContent = '已收藏';
        } else {
            icon.classList.remove('fas');
            icon.classList.remove('text-red-500');
            icon.classList.add('far');
            favoriteBtn.querySelector('span').textContent = '收藏';
        }
    }
    
    // 处理商家、优惠等按钮点击
    function handleActionBtn() {
        const btnText = this.querySelector('span').textContent;
        console.log(`点击了${btnText}按钮`);
        
        // 在实际应用中，这里可以添加相应的逻辑
        alert(`查看${btnText}信息`);
    }
    
    // 添加响应式处理
    function handleResize() {
        const viewportWidth = window.innerWidth;
        
        // 这里可以根据屏幕宽度调整UI
    }
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
});
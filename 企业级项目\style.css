/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #ff4d4f;
    --secondary-color: #ff7875;
    --text-color: #333;
    --text-light: #666;
    --light-gray: #f5f5f5;
    --white: #fff;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --border: 1px solid #e8e8e8;
    --radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: #fafafa;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

ul {
    list-style: none;
}

button {
    cursor: pointer;
    border: none;
    background: none;
    font-family: inherit;
    transition: var(--transition);
}

input, select {
    font-family: inherit;
    outline: none;
    border: var(--border);
    padding: 8px 12px;
    border-radius: var(--radius);
    transition: var(--transition);
}

input:focus, select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.section {
    padding: 60px 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.section-header h2 {
    font-size: 24px;
    font-weight: 600;
}

.view-all {
    color: var(--text-light);
    font-size: 14px;
}

.view-all:hover {
    color: var(--primary-color);
}

/* 顶部导航栏 */
header {
    background-color: var(--white);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 15px 0;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.logo i {
    margin-right: 10px;
}

.nav-links {
    display: flex;
    gap: 30px;
    margin: 0 40px;
}

.nav-links a {
    font-weight: 500;
    position: relative;
}

.nav-links a.active,
.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a.active::after,
.nav-links a:hover::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    display: flex;
    align-items: center;
    background-color: var(--light-gray);
    padding: 6px 12px;
    border-radius: 20px;
}

.search-box input {
    border: none;
    background: transparent;
    padding: 0;
    width: 200px;
}

.search-box button {
    color: var(--text-light);
}

.btn-cart, .btn-user {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-cart:hover, .btn-user:hover {
    background-color: #e8e8e8;
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--primary-color);
    color: var(--white);
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

.menu-toggle {
    display: none;
    font-size: 24px;
    cursor: pointer;
}

/* 移动端菜单 */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 280px;
    height: 100vh;
    background-color: var(--white);
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 1100;
    transition: var(--transition);
    padding: 20px;
}

.mobile-menu.active {
    right: 0;
}

.mobile-links {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 40px;
}

.mobile-links a {
    font-size: 18px;
    font-weight: 500;
    padding: 10px 0;
}

.mobile-actions {
    margin-top: auto;
}

.mobile-actions .btn-user {
    width: 100%;
    height: auto;
    border-radius: var(--radius);
    padding: 12px;
    justify-content: flex-start;
    gap: 10px;
    font-size: 16px;
    background-color: var(--primary-color);
    color: var(--white);
}

/* 英雄区域 - 轮播图样式 */
.hero {
    position: relative;
    overflow: hidden;
    color: var(--white);
    height: 500px;
}

.carousel {
    position: relative;
    width: 100%;
    height: 100%;
}

.carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.carousel-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
    display: flex;
    align-items: center;
}

.carousel-item.active {
    opacity: 1;
    z-index: 1;
}

.carousel-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    filter: brightness(0.6);
}

.carousel-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    padding: 0 40px;
}

.carousel-content h1 {
    font-size: 48px;
    margin-bottom: 20px;
    font-weight: 700;
    line-height: 1.2;
}

.carousel-content p {
    font-size: 18px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.delivery-location {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--text-color);
    padding: 12px 20px;
    border-radius: 30px;
    max-width: 400px;
}

.delivery-location i {
    color: var(--primary-color);
    margin-right: 10px;
}

.change-location {
    margin-left: auto;
    background: none;
    color: var(--text-light);
}

/* 轮播控制按钮 */
.carousel-control {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    transition: background-color 0.3s ease;
}

.carousel-control:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

.carousel-control.prev {
    left: 20px;
}

.carousel-control.next {
    right: 20px;
}

/* 轮播指示器 */
.carousel-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 2;
}

.indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    transition: background-color 0.3s ease;
}

.indicator.active {
    background-color: var(--white);
    width: 20px;
    border-radius: 5px;
}

/* 餐厅列表 */
.restaurant-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.restaurant-card {
    background-color: var(--white);
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.restaurant-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.card-img {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.card-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.restaurant-card:hover .card-img img {
    transform: scale(1.05);
}

.restaurant-tags {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    gap: 5px;
}

.tag {
    background-color: var(--primary-color);
    color: var(--white);
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
}

.card-content {
    padding: 16px;
}

.card-content h3 {
    font-size: 18px;
    margin-bottom: 10px;
}

.restaurant-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 10px;
}

.rating {
    display: flex;
    align-items: center;
    color: #ffb300;
}

.rating i {
    margin-right: 4px;
}

.food-types {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.food-types span {
    background-color: var(--light-gray);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    color: var(--text-light);
}

/* 分类浏览 */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
}

.category-card {
    background-color: var(--white);
    border-radius: var(--radius);
    padding: 16px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.category-card img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 50%;
    margin: 0 auto 10px;
}

.category-card span {
    font-size: 14px;
    font-weight: 500;
}

/* 优惠活动 */
.bg-light {
    background-color: var(--light-gray);
}

.deal-tabs {
    display: flex;
    gap: 10px;
}

.deal-tabs .tab {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    background-color: var(--white);
}

.deal-tabs .tab.active,
.deal-tabs .tab:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.deals-container {
    margin-top: 20px;
}

.deal-banner {
    position: relative;
    height: 300px;
    border-radius: var(--radius);
    overflow: hidden;
    margin-bottom: 20px;
}

.deal-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.deal-info {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
    color: var(--white);
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.deal-info h3 {
    font-size: 32px;
    margin-bottom: 10px;
}

.deal-info p {
    font-size: 18px;
    margin-bottom: 20px;
}

.countdown {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 16px;
}

.countdown .time {
    background-color: var(--primary-color);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
}

.deals-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
}

.deal-item {
    background-color: var(--white);
    padding: 16px;
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: var(--shadow);
}

.discount {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 16px;
    font-weight: 600;
}

.deal-desc {
    color: var(--text-light);
}

/* 热门推荐 */
.food-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
}

.food-card {
    background-color: var(--white);
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.food-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.food-img {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.food-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.food-card:hover .food-img img {
    transform: scale(1.05);
}

.add-to-cart {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.food-card:hover .add-to-cart {
    opacity: 1;
}

.add-to-cart:hover {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.food-info {
    padding: 16px;
}

.food-info h3 {
    font-size: 16px;
    margin-bottom: 5px;
}

.food-desc {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 10px;
    line-height: 1.4;
}

.food-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.food-meta .price {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.food-meta .sales {
    font-size: 14px;
    color: var(--text-light);
}

/* 用户评价 */
.reviews-slider {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding-bottom: 20px;
}

.reviews-slider::-webkit-scrollbar {
    height: 6px;
}

.reviews-slider::-webkit-scrollbar-track {
    background: var(--light-gray);
    border-radius: 3px;
}

.reviews-slider::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.review-item {
    flex: 0 0 380px;
    background-color: var(--white);
    padding: 20px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
}

.reviewer-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.reviewer-info img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
}

.reviewer-info h4 {
    margin-bottom: 5px;
}

.review-content {
    color: var(--text-color);
    margin-bottom: 15px;
    line-height: 1.6;
}

.review-meta {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: var(--text-light);
}

/* 下载APP */
.app-promo {
    background: linear-gradient(to right, #ff4d4f, #ff7875);
    color: var(--white);
}

.app-promo-content {
    display: flex;
    align-items: center;
    gap: 40px;
}

.app-info {
    flex: 1;
}

.app-info h2 {
    font-size: 36px;
    margin-bottom: 20px;
}

.app-info p {
    font-size: 18px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.app-download {
    display: flex;
    gap: 20px;
}

.download-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: var(--white);
    color: var(--primary-color);
    padding: 12px 24px;
    border-radius: var(--radius);
    font-size: 16px;
    font-weight: 600;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.download-btn i {
    font-size: 24px;
}

.download-btn div {
    display: flex;
    flex-direction: column;
}

.download-btn div span:first-child {
    font-size: 12px;
    opacity: 0.8;
}

.app-image img {
    max-width: 250px;
    border-radius: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* 购物车侧边栏 */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    max-width: 400px;
    height: 100vh;
    background-color: var(--white);
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 1100;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

.cart-sidebar.active {
    right: 0;
}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: var(--border);
}

.cart-header h3 {
    font-size: 18px;
}

.close-btn {
    font-size: 20px;
    color: var(--text-light);
}

.close-btn:hover {
    color: var(--text-color);
}

.cart-items {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-light);
}

.empty-cart i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.cart-item {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: var(--border);
}

.cart-item-img {
    width: 80px;
    height: 80px;
    border-radius: var(--radius);
    overflow: hidden;
}

.cart-item-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-size: 16px;
    margin-bottom: 8px;
}

.cart-item-price {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.control-btn:hover {
    background-color: #e8e8e8;
}

.cart-footer {
    padding: 20px;
    border-top: var(--border);
    background-color: var(--white);
}

.cart-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.cart-summary span:last-child {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.checkout-btn {
    width: 100%;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 15px;
    border-radius: var(--radius);
    font-size: 16px;
    font-weight: 600;
}

.checkout-btn:hover {
    background-color: var(--secondary-color);
}

/* 结算弹窗 */
.checkout-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1200;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.checkout-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--radius);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: var(--border);
}

.modal-body {
    padding: 20px;
}

.order-section {
    margin-bottom: 25px;
}

.order-section h4 {
    margin-bottom: 15px;
    font-size: 16px;
}

.delivery-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item label {
    font-size: 14px;
    color: var(--text-light);
}

.payment-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.payment-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    border-radius: var(--radius);
    border: var(--border);
    cursor: pointer;
}

.payment-option input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.order-items {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.order-summary {
    border-top: var(--border);
    padding-top: 15px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.summary-item.total {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 18px;
    margin-top: 10px;
}

.modal-footer {
    padding: 20px;
    border-top: var(--border);
}

.confirm-order-btn {
    width: 100%;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 15px;
    border-radius: var(--radius);
    font-size: 16px;
    font-weight: 600;
}

.confirm-order-btn:hover {
    background-color: var(--secondary-color);
}

/* 订单成功弹窗 */
.success-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1200;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.success-modal.active {
    opacity: 1;
    visibility: visible;
}

.success-icon {
    font-size: 64px;
    color: #52c41a;
    margin-bottom: 20px;
    text-align: center;
}

.success-modal h3 {
    text-align: center;
    margin-bottom: 10px;
}

.success-modal p {
    text-align: center;
    color: var(--text-light);
    margin-bottom: 20px;
}

.close-success {
    display: block;
    margin: 0 auto;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 10px 20px;
    border-radius: var(--radius);
}

.close-success:hover {
    background-color: var(--secondary-color);
}

/* 遮罩层 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 900;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* 页脚 */
footer {
    background-color: var(--text-color);
    color: var(--white);
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.footer-logo i {
    font-size: 32px;
    color: var(--primary-color);
}

.footer-logo span {
    font-size: 24px;
    font-weight: 700;
}

.footer-logo p {
    color: #ccc;
}

.footer-links h4 {
    font-size: 16px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-links h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-links a {
    display: block;
    color: #ccc;
    margin-bottom: 10px;
    font-size: 14px;
}

.footer-links a:hover {
    color: var(--white);
    transform: translateX(5px);
}

.social-icons {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    margin-bottom: 0;
}

.social-icons a:hover {
    background-color: var(--primary-color);
    transform: none;
}

.app-download {
    text-align: center;
}

.app-download img {
    margin: 0 auto 10px;
    border-radius: var(--radius);
}

.app-download span {
    font-size: 14px;
    color: #ccc;
}

.footer-bottom {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    margin-bottom: 15px;
    color: #ccc;
    font-size: 14px;
}

.footer-policy {
    display: flex;
    gap: 20px;
}

.footer-policy a {
    color: #ccc;
    font-size: 14px;
}

.footer-policy a:hover {
    color: var(--white);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .nav-links {
        gap: 20px;
        margin: 0 20px;
    }
    
    .app-promo-content {
        gap: 30px;
    }
    
    .app-info h2 {
        font-size: 30px;
    }
}

@media (max-width: 768px) {
    .nav-links,
    .nav-actions {
        display: none;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .hero h1 {
        font-size: 36px;
    }
    
    .restaurant-grid,
    .deals-list {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .food-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
    
    .app-promo-content {
        flex-direction: column;
        text-align: center;
    }
    
    .app-image img {
        max-width: 200px;
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
}

@media (max-width: 480px) {
    .section {
        padding: 40px 0;
    }
    
    .section-header h2 {
        font-size: 20px;
    }
    
    .hero {
        padding: 60px 0;
    }
    
    .hero h1 {
        font-size: 28px;
    }
    
    .restaurant-grid,
    .deals-list {
        grid-template-columns: 1fr;
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 10px;
    }
    
    .category-card {
        padding: 12px;
    }
    
    .category-card img {
        width: 60px;
        height: 60px;
    }
    
    .deal-banner {
        height: 200px;
    }
    
    .deal-info {
        padding: 20px;
    }
    
    .deal-info h3 {
        font-size: 24px;
    }
    
    .food-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .review-item {
        flex: 0 0 300px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .modal-content {
        margin: 20px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.restaurant-card,
.food-card,
.category-card {
    animation: fadeIn 0.5s ease;
}

.restaurant-card:nth-child(2),
.food-card:nth-child(2),
.category-card:nth-child(2) {
    animation-delay: 0.1s;
}

.restaurant-card:nth-child(3),
.food-card:nth-child(3),
.category-card:nth-child(3) {
    animation-delay: 0.2s;
}

.restaurant-card:nth-child(4),
.food-card:nth-child(4),
.category-card:nth-child(4) {
    animation-delay: 0.3s;
}
import requests
from bs4 import BeautifulSoup
import csv
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuoteScraper:
    def __init__(self):
        self.base_url = 'http://quotes.toscrape.com'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.quotes = []

    def fetch_page(self, url):
        """获取页面内容"""
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()  # 如果状态码不是200，抛出异常
            return response.text
        except requests.RequestException as e:
            logger.error(f'获取页面 {url} 失败: {e}')
            return None

    def parse_page(self, html):
        """解析页面内容，提取名言数据"""
        if not html:
            return False

        soup = BeautifulSoup(html, 'html.parser')
        quote_elements = soup.find_all('div', class_='quote')

        if not quote_elements:
            return False  # 没有找到名言，可能是最后一页

        for quote_element in quote_elements:
            text = quote_element.find('span', class_='text').text.strip()
            author = quote_element.find('small', class_='author').text.strip()
            tags = [tag.text.strip() for tag in quote_element.find_all('a', class_='tag')]

            self.quotes.append({
                'text': text,
                'author': author,
                'tags': ', '.join(tags)
            })

        # 检查是否有下一页
        next_button = soup.find('li', class_='next')
        if next_button:
            next_page_url = self.base_url + next_button.find('a')['href']
            return next_page_url
        return None

    def save_to_csv(self, filename='quotes.csv'):
        """将爬取的数据保存到CSV文件"""
        if not self.quotes:
            logger.warning('没有数据可保存')
            return

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['text', 'author', 'tags']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for quote in self.quotes:
                    writer.writerow(quote)

            logger.info(f'成功保存 {len(self.quotes)} 条名言到 {filename}')
        except Exception as e:
            logger.error(f'保存数据到CSV文件失败: {e}')

    def run(self):
        """运行爬虫"""
        logger.info('开始爬取 Quotes to Scrape 网站...')
        start_time = time.time()

        current_url = self.base_url
        while current_url:
            logger.info(f'正在爬取: {current_url}')
            html = self.fetch_page(current_url)
            current_url = self.parse_page(html)
            if current_url:
                time.sleep(1)  # 礼貌爬取，间隔1秒

        self.save_to_csv()
        end_time = time.time()
        logger.info(f'爬取完成，共耗时 {end_time - start_time:.2f} 秒')

if __name__ == '__main__':
    scraper = QuoteScraper()
    scraper.run()
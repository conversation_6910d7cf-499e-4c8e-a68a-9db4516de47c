* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #333;
    font-family: Arial, sans-serif;
}

.game-container {
    text-align: center;
}

#gameCanvas {
    background-color: #000;
    border: 2px solid #fff;
    display: block;
    margin: 0 auto;
}

.game-info {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    color: #fff;
    font-size: 18px;
}

.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 15px;
}

button {
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #45a049;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}
package java;
public class Book {
    private String isbn;
    private String title;
    private String author;
    private String publisher;
    private int publicationYear;
    private boolean available;
    
    // 构造方法
    public Book(String isbn, String title, String author, String publisher, int publicationYear) {
        this.isbn = isbn;
        this.title = title;
        this.author = author;
        this.publisher = publisher;
        this.publicationYear = publicationYear;
        this.available = true; // 新书默认可借阅
    }
    
    // getter和setter方法
    public String getIsbn() {
        return isbn;
    }
    
    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getAuthor() {
        return author;
    }
    
    public void setAuthor(String author) {
        this.author = author;
    }
    
    public String getPublisher() {
        return publisher;
    }
    
    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }
    
    public int getPublicationYear() {
        return publicationYear;
    }
    
    public void setPublicationYear(int publicationYear) {
        this.publicationYear = publicationYear;
    }
    
    public boolean isAvailable() {
        return available;
    }
    
    public void setAvailable(boolean available) {
        this.available = available;
    }
    
    // 重写toString方法，方便打印图书信息
    @Override
    public String toString() {
        return "ISBN: " + isbn + ", 书名: " + title + ", 作者: " + author + ", 出版社: " + publisher + ", 出版年份: " + publicationYear + ", 状态: " + (available ? "可借阅" : "已借出");
    }
}
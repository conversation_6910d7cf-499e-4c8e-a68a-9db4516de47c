// 登录注册页面的脚本

document.addEventListener('DOMContentLoaded', function() {
    // 获取元素
    const loginTab = document.getElementById('loginTab');
    const registerTab = document.getElementById('registerTab');
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const toggleLoginPassword = document.getElementById('toggleLoginPassword');
    const toggleRegisterPassword = document.getElementById('toggleRegisterPassword');
    const loginPassword = document.getElementById('loginPassword');
    const registerPassword = document.getElementById('registerPassword');
    const sendCodeBtn = document.querySelector('.send-code-btn');
    
    // 切换登录/注册表单
    loginTab.addEventListener('click', function() {
        loginTab.classList.add('active');
        registerTab.classList.remove('active');
        loginForm.classList.add('active');
        registerForm.classList.remove('active');
    });
    
    registerTab.addEventListener('click', function() {
        registerTab.classList.add('active');
        loginTab.classList.remove('active');
        registerForm.classList.add('active');
        loginForm.classList.remove('active');
    });
    
    // 切换密码可见性
    toggleLoginPassword.addEventListener('click', function() {
        togglePasswordVisibility(loginPassword, toggleLoginPassword);
    });
    
    toggleRegisterPassword.addEventListener('click', function() {
        togglePasswordVisibility(registerPassword, toggleRegisterPassword);
    });
    
    // 发送验证码
    sendCodeBtn.addEventListener('click', function() {
        const phone = document.getElementById('registerPhone').value;
        if (!validatePhone(phone)) {
            alert('请输入正确的手机号码');
            return;
        }
        
        // 倒计时功能
        let countdown = 60;
        sendCodeBtn.disabled = true;
        sendCodeBtn.textContent = `${countdown}秒后重发`;
        
        const timer = setInterval(() => {
            countdown--;
            sendCodeBtn.textContent = `${countdown}秒后重发`;
            
            if (countdown <= 0) {
                clearInterval(timer);
                sendCodeBtn.disabled = false;
                sendCodeBtn.textContent = '获取验证码';
            }
        }, 1000);
        
        // 这里可以添加发送验证码的API调用
        console.log('发送验证码到手机:', phone);
    });
    
    // 登录表单提交
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const phone = document.getElementById('loginPhone').value;
        const password = loginPassword.value;
        
        if (!validatePhone(phone)) {
            alert('请输入正确的手机号码');
            return;
        }
        
        if (password.length < 6) {
            alert('密码长度不能少于6位');
            return;
        }
        
        // 这里可以添加登录API调用
        console.log('登录信息:', { phone, password });
        
        // 登录成功后跳转到首页
        alert('登录成功！');
        window.location.href = 'index.html';
    });
    
    // 注册表单提交
    registerForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const phone = document.getElementById('registerPhone').value;
        const code = document.getElementById('verificationCode').value;
        const password = registerPassword.value;
        
        if (!validatePhone(phone)) {
            alert('请输入正确的手机号码');
            return;
        }
        
        if (!code || code.length !== 6 || !/^\d+$/.test(code)) {
            alert('请输入6位数字验证码');
            return;
        }
        
        if (password.length < 6 || password.length > 20) {
            alert('密码长度应在6-20位之间');
            return;
        }
        
        // 这里可以添加注册API调用
        console.log('注册信息:', { phone, code, password });
        
        // 注册成功后跳转到首页
        alert('注册成功！');
        window.location.href = 'index.html';
    });
    
    // 工具函数
    function togglePasswordVisibility(input, button) {
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
        
        // 切换图标
        const icon = button.querySelector('i');
        if (type === 'password') {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        } else {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        }
    }
    
    function validatePhone(phone) {
        // 简单的手机号验证规则
        return /^1[3-9]\d{9}$/.test(phone);
    }
});
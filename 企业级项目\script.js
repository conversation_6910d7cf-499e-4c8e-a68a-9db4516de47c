// 等待DOM加载完成
 document.addEventListener('DOMContentLoaded', function() {
    // 轮播图相关变量（需要在函数调用前声明）
    let currentSlide = 0;
    let slideInterval;
    
    // 元素获取
    const menuToggle = document.querySelector('.menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    const overlay = document.querySelector('.overlay');
    const cartBtn = document.getElementById('cartBtn');
    const cartSidebar = document.querySelector('.cart-sidebar');
    const closeCart = document.getElementById('closeCart');
    const checkoutBtn = document.getElementById('checkoutBtn');
    const checkoutModal = document.querySelector('.checkout-modal');
    const confirmOrderBtn = document.querySelector('.confirm-order-btn');
    const successModal = document.querySelector('.success-modal');
    const closeSuccess = document.querySelector('.close-success');
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    const cartItemsContainer = document.querySelector('.cart-items');
    const cartCount = document.querySelector('.cart-count');
    const itemCount = document.getElementById('itemCount');
    const totalPrice = document.getElementById('totalPrice');
    const orderSubtotal = document.getElementById('orderSubtotal');
    const orderTotal = document.getElementById('orderTotal');
    const orderItemsContainer = document.querySelector('.order-items');
    const countdownElements = document.querySelectorAll('.countdown .time');
    const dealTabs = document.querySelectorAll('.deal-tabs .tab');
    const navLinks = document.querySelectorAll('.nav-links a');
    const mobileLinks = document.querySelectorAll('.mobile-links a');
    
    // 轮播图相关元素
    const carouselItems = document.querySelectorAll('.carousel-item');
    const carouselPrev = document.querySelector('.carousel-control.prev');
    const carouselNext = document.querySelector('.carousel-control.next');
    const indicators = document.querySelectorAll('.indicator');
    
    // 购物车数据
    let cart = [];
    
    // 初始化
    init();
    
    function init() {
        // 事件监听
        setupEventListeners();
        
        // 启动倒计时
        startCountdown();
        
        // 平滑滚动
        setupSmoothScroll();
        
        // 标签页切换
        setupTabSwitching();
        
        // 初始化轮播图
        initCarousel();
    }
    
    function setupEventListeners() {
        // 移动端菜单
        menuToggle.addEventListener('click', toggleMobileMenu);
        overlay.addEventListener('click', closeAllModals);
        
        // 购物车
        cartBtn.addEventListener('click', openCart);
        closeCart.addEventListener('click', closeCartSidebar);
        checkoutBtn.addEventListener('click', openCheckout);
        
        // 结算流程
        confirmOrderBtn.addEventListener('click', processOrder);
        closeSuccess.addEventListener('click', closeSuccessModal);
        
        // 添加到购物车
        addToCartButtons.forEach(button => {
            button.addEventListener('click', addToCart);
        });
        
        // 关闭按钮
        document.querySelectorAll('.modal-content .close-btn').forEach(btn => {
            btn.addEventListener('click', closeCheckout);
        });
        
        // 轮播图控制
        if (carouselPrev && carouselNext) {
            carouselPrev.addEventListener('click', () => changeSlide(-1));
            carouselNext.addEventListener('click', () => changeSlide(1));
        }
        
        // 轮播图指示器
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => goToSlide(index));
        });
    }
    
    // 移动端菜单切换
    function toggleMobileMenu() {
        mobileMenu.classList.toggle('active');
        overlay.classList.toggle('active');
        document.body.classList.toggle('overflow-hidden');
    }
    
    // 打开购物车
    function openCart() {
        cartSidebar.classList.add('active');
        overlay.classList.add('active');
        document.body.classList.add('overflow-hidden');
        updateCartUI();
    }
    
    // 关闭购物车
    function closeCartSidebar() {
        cartSidebar.classList.remove('active');
        overlay.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
    }
    
    // 打开结算弹窗
    function openCheckout() {
        if (cart.length === 0) {
            alert('购物车为空，请先添加商品！');
            return;
        }
        
        cartSidebar.classList.remove('active');
        checkoutModal.classList.add('active');
        updateOrderSummary();
    }
    
    // 关闭结算弹窗
    function closeCheckout() {
        checkoutModal.classList.remove('active');
        overlay.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
    }
    
    // 处理订单
    function processOrder() {
        const name = document.querySelector('input[type="text"][placeholder="请输入收货人姓名"]').value;
        const phone = document.querySelector('input[type="tel"][placeholder="请输入联系电话"]').value;
        const address = document.querySelector('input[type="text"][placeholder="请输入详细地址"]').value;
        
        if (!name || !phone || !address) {
            alert('请填写完整的配送信息！');
            return;
        }
        
        checkoutModal.classList.remove('active');
        successModal.classList.add('active');
        
        // 清空购物车
        cart = [];
        updateCartUI();
    }
    
    // 关闭订单成功弹窗
    function closeSuccessModal() {
        successModal.classList.remove('active');
        overlay.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
    }
    
    // 关闭所有模态框
    function closeAllModals() {
        mobileMenu.classList.remove('active');
        cartSidebar.classList.remove('active');
        checkoutModal.classList.remove('active');
        successModal.classList.remove('active');
        overlay.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
    }
    
    // 添加商品到购物车
    function addToCart(event) {
        const button = event.target.closest('.add-to-cart');
        const id = button.dataset.id;
        const name = button.dataset.name;
        const price = parseFloat(button.dataset.price);
        const image = button.dataset.image;
        
        // 检查商品是否已在购物车中
        const existingItem = cart.find(item => item.id === id);
        
        if (existingItem) {
            existingItem.quantity++;
        } else {
            cart.push({
                id,
                name,
                price,
                image,
                quantity: 1
            });
        }
        
        // 显示添加成功动画
        showAddToCartAnimation(button);
        
        // 更新购物车UI
        updateCartUI();
    }
    
    // 显示添加到购物车动画
    function showAddToCartAnimation(button) {
        const animation = document.createElement('span');
        animation.className = 'add-animation';
        animation.innerHTML = '\u2713';
        animation.style.position = 'fixed';
        animation.style.width = '30px';
        animation.style.height = '30px';
        animation.style.borderRadius = '50%';
        animation.style.backgroundColor = '#ff4d4f';
        animation.style.color = 'white';
        animation.style.display = 'flex';
        animation.style.alignItems = 'center';
        animation.style.justifyContent = 'center';
        animation.style.zIndex = '1500';
        animation.style.opacity = '0';
        
        // 获取按钮位置
        const rect = button.getBoundingClientRect();
        animation.style.left = `${rect.left + rect.width / 2}px`;
        animation.style.top = `${rect.top + rect.height / 2}px`;
        
        document.body.appendChild(animation);
        
        // 动画效果
        setTimeout(() => {
            animation.style.transition = 'all 0.8s cubic-bezier(0.2, 1, 0.3, 1)';
            animation.style.opacity = '1';
            animation.style.transform = 'scale(1.2)';
        }, 10);
        
        // 获取购物车图标位置
        const cartRect = cartBtn.getBoundingClientRect();
        
        setTimeout(() => {
            animation.style.transition = 'all 1s cubic-bezier(0.2, 1, 0.3, 1)';
            animation.style.left = `${cartRect.left + cartRect.width / 2}px`;
            animation.style.top = `${cartRect.top + cartRect.height / 2}px`;
            animation.style.opacity = '0';
            animation.style.transform = 'scale(0.5)';
        }, 300);
        
        // 移除动画元素
        setTimeout(() => {
            document.body.removeChild(animation);
        }, 1300);
    }
    
    // 更新购物车UI
    function updateCartUI() {
        // 更新购物车数量
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
        
        // 清空购物车容器
        cartItemsContainer.innerHTML = '';
        
        if (cart.length === 0) {
            // 显示空购物车状态
            const emptyCart = document.createElement('div');
            emptyCart.className = 'empty-cart';
            emptyCart.innerHTML = `
                <i class="fas fa-shopping-cart"></i>
                <p>购物车还是空的</p>
                <p>快去添加美食吧！</p>
            `;
            cartItemsContainer.appendChild(emptyCart);
            
            // 禁用结算按钮
            checkoutBtn.disabled = true;
            checkoutBtn.style.opacity = '0.5';
            
            // 更新计数和总价
            itemCount.textContent = '0';
            totalPrice.textContent = '¥0';
        } else {
            // 启用结算按钮
            checkoutBtn.disabled = false;
            checkoutBtn.style.opacity = '1';
            
            // 添加购物车项目
            cart.forEach(item => {
                const cartItem = document.createElement('div');
                cartItem.className = 'cart-item';
                cartItem.innerHTML = `
                    <div class="cart-item-img">
                        <img src="${item.image}" alt="${item.name}">
                    </div>
                    <div class="cart-item-info">
                        <h4 class="cart-item-name">${item.name}</h4>
                        <div class="cart-item-price">¥${item.price.toFixed(2)}</div>
                        <div class="cart-item-controls">
                            <button class="control-btn decrease" data-id="${item.id}">-</button>
                            <span class="quantity">${item.quantity}</span>
                            <button class="control-btn increase" data-id="${item.id}">+</button>
                        </div>
                    </div>
                `;
                cartItemsContainer.appendChild(cartItem);
            });
            
            // 添加增减按钮事件监听
            document.querySelectorAll('.decrease').forEach(btn => {
                btn.addEventListener('click', decreaseQuantity);
            });
            document.querySelectorAll('.increase').forEach(btn => {
                btn.addEventListener('click', increaseQuantity);
            });
            
            // 更新计数和总价
            itemCount.textContent = totalItems;
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            totalPrice.textContent = `¥${total.toFixed(2)}`;
        }
    }
    
    // 减少商品数量
    function decreaseQuantity(event) {
        const id = event.target.closest('.decrease').dataset.id;
        const item = cart.find(item => item.id === id);
        
        if (item.quantity > 1) {
            item.quantity--;
        } else {
            cart = cart.filter(item => item.id !== id);
        }
        
        updateCartUI();
    }
    
    // 增加商品数量
    function increaseQuantity(event) {
        const id = event.target.closest('.increase').dataset.id;
        const item = cart.find(item => item.id === id);
        
        item.quantity++;
        
        updateCartUI();
    }
    
    // 更新订单摘要
    function updateOrderSummary() {
        // 清空订单容器
        orderItemsContainer.innerHTML = '';
        
        // 添加订单项
        cart.forEach(item => {
            const orderItem = document.createElement('div');
            orderItem.className = 'cart-item';
            orderItem.innerHTML = `
                <div class="cart-item-img">
                    <img src="${item.image}" alt="${item.name}">
                </div>
                <div class="cart-item-info">
                    <h4 class="cart-item-name">${item.name}</h4>
                    <div class="cart-item-price">¥${item.price.toFixed(2)} x ${item.quantity}</div>
                </div>
            `;
            orderItemsContainer.appendChild(orderItem);
        });
        
        // 计算总价
        const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const deliveryFee = 5; // 配送费
        const total = subtotal + deliveryFee;
        
        // 更新价格显示
        orderSubtotal.textContent = `¥${subtotal.toFixed(2)}`;
        orderTotal.textContent = `¥${total.toFixed(2)}`;
    }
    
    // 启动倒计时
    function startCountdown() {
        // 设置倒计时时间（8小时45分钟32秒）
        let hours = 8;
        let minutes = 45;
        let seconds = 32;
        
        function updateCountdown() {
            // 更新倒计时显示
            countdownElements[0].textContent = hours.toString().padStart(2, '0');
            countdownElements[1].textContent = minutes.toString().padStart(2, '0');
            countdownElements[2].textContent = seconds.toString().padStart(2, '0');
            
            // 减少倒计时
            seconds--;
            
            if (seconds < 0) {
                seconds = 59;
                minutes--;
                
                if (minutes < 0) {
                    minutes = 59;
                    hours--;
                    
                    if (hours < 0) {
                        // 倒计时结束，可以重置或做其他处理
                        hours = 8;
                        minutes = 45;
                        seconds = 32;
                    }
                }
            }
        }
        
        // 立即更新一次
        updateCountdown();
        
        // 每秒更新一次
        setInterval(updateCountdown, 1000);
    }
    
    // 设置平滑滚动
    function setupSmoothScroll() {
        // 桌面导航
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80, // 考虑导航栏高度
                        behavior: 'smooth'
                    });
                    
                    // 更新活动链接
                    navLinks.forEach(navLink => navLink.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });
        
        // 移动端导航
        mobileLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80, // 考虑导航栏高度
                        behavior: 'smooth'
                    });
                    
                    // 关闭移动端菜单
                    mobileMenu.classList.remove('active');
                    overlay.classList.remove('active');
                    document.body.classList.remove('overflow-hidden');
                }
            });
        });
        
        // 滚动时更新活动链接
        window.addEventListener('scroll', function() {
            const scrollPosition = window.scrollY;
            
            // 找出当前视图中的章节
            document.querySelectorAll('section[id]').forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.offsetHeight;
                const sectionId = section.getAttribute('id');
                
                if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                    // 更新桌面导航
                    navLinks.forEach(link => {
                        link.classList.remove('active');
                        if (link.getAttribute('href') === `#${sectionId}`) {
                            link.classList.add('active');
                        }
                    });
                }
            });
        });
    }
    
    // 设置标签页切换
    function setupTabSwitching() {
        dealTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签页的活动状态
                dealTabs.forEach(t => t.classList.remove('active'));
                
                // 添加当前标签页的活动状态
                this.classList.add('active');
                
                // 这里可以根据需要加载不同的优惠内容
                // 简单演示：根据标签页索引显示不同的内容
                const tabIndex = Array.from(dealTabs).indexOf(this);
                console.log(`切换到标签页：${tabIndex}`);
                // 实际应用中可以根据标签页索引加载不同的内容
            });
        });
    }
    
    // 轮播图相关函数
    function initCarousel() {
        if (carouselItems.length === 0) return;
        
        // 自动轮播
        startSlideInterval();
        
        // 鼠标悬停时暂停，离开时继续
        const carousel = document.querySelector('.carousel');
        if (carousel) {
            carousel.addEventListener('mouseenter', stopSlideInterval);
            carousel.addEventListener('mouseleave', startSlideInterval);
        }
    }
    
    function startSlideInterval() {
        // 每5秒自动切换一张幻灯片
        slideInterval = setInterval(() => changeSlide(1), 5000);
    }
    
    function stopSlideInterval() {
        clearInterval(slideInterval);
    }
    
    function changeSlide(direction) {
        // 计算下一张幻灯片的索引
        const newIndex = (currentSlide + direction + carouselItems.length) % carouselItems.length;
        goToSlide(newIndex);
    }
    
    function goToSlide(index) {
        // 移除当前幻灯片的活动状态
        carouselItems[currentSlide].classList.remove('active');
        indicators[currentSlide].classList.remove('active');
        
        // 更新当前幻灯片索引
        currentSlide = index;
        
        // 添加新幻灯片的活动状态
        carouselItems[currentSlide].classList.add('active');
        indicators[currentSlide].classList.add('active');
    }
    
    // 添加响应式处理
    function handleResize() {
        const viewportWidth = window.innerWidth;
        
        // 如果屏幕宽度大于768px，关闭移动端菜单
        if (viewportWidth > 768) {
            mobileMenu.classList.remove('active');
            overlay.classList.remove('active');
            document.body.classList.remove('overflow-hidden');
        }
    }
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
});
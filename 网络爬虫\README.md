# 名言爬虫项目

这个项目是一个简单的Python爬虫，用于爬取 [Quotes to Scrape](http://quotes.toscrape.com) 网站上的名言数据，并将其保存到CSV文件中。

## 功能特点
- 爬取网站上的所有名言、作者和标签
- 自动翻页，获取所有页面的数据
- 数据保存到CSV文件，方便后续分析和使用
- 包含日志记录，便于追踪爬虫运行状态
- 添加了请求延迟，遵循网站爬取规则

## 项目结构
```
网络爬虫/
├── quote_scraper.py  # 主爬虫文件
├── requirements.txt  # 依赖库列表
└── README.md         # 项目说明文档
```

## 使用方法
1. 克隆或下载本项目到本地
2. 安装依赖库：
   ```
   pip install -r requirements.txt
   ```
3. 运行爬虫：
   ```
   python quote_scraper.py
   ```
4. 爬取完成后，数据将保存到当前目录下的 `quotes.csv` 文件中

## 数据格式
CSV文件包含以下字段：
- `text`: 名言内容
- `author`: 作者
- `tags`: 标签（多个标签用逗号分隔）

## 注意事项
- 本项目仅用于学习和研究目的，请勿用于商业用途
- 爬取数据时请遵守网站的robots.txt规则
- 不要频繁运行爬虫，以免对网站服务器造成压力